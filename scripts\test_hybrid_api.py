#!/usr/bin/env python3
"""
Test script for the hybrid backtesting API endpoint.
"""

import requests
import json
import time
from datetime import datetime, timedelta

# API base URL (adjust as needed)
BASE_URL = "http://localhost:8000/api/v1/backtesting"

def test_hybrid_backtest_api():
    """Test the hybrid backtest API endpoint."""
    print("🚀 Testing Hybrid Backtest API")
    print("="*50)
    
    # Test data
    request_data = {
        "symbol": "NIFTY50",
        "timeframe": 30,
        "start_date": None,  # Will use default
        "end_date": None,    # Will use default
        "initial_capital": 30000.0,
        "margin": 0.1,
        "commission": 0.0,
        "generate_files": False,  # Skip file generation for performance
        "strategy_config": None
    }
    
    try:
        print("📊 Sending hybrid backtest request...")
        print(f"Request data: {json.dumps(request_data, indent=2)}")
        
        start_time = time.time()
        
        # Make API request
        response = requests.post(
            f"{BASE_URL}/hybrid/run",
            json=request_data,
            timeout=120  # 2 minutes timeout
        )
        
        execution_time = time.time() - start_time
        
        print(f"⏱️  API call completed in {execution_time:.2f} seconds")
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Hybrid backtest API call successful!")
            
            # Extract key metrics
            if 'results' in result:
                results = result['results']
                custom_metrics = results.get('custom_metrics', {})
                
                print("\n📈 Key Results:")
                print(f"  • Total Trades: {custom_metrics.get('Total Trades', 'N/A')}")
                print(f"  • Win Rate: {custom_metrics.get('Win Rate (%)', 'N/A'):.2f}%")
                print(f"  • Total Return: {custom_metrics.get('Total Return (%)', 'N/A'):.2f}%")
                print(f"  • Risk-to-Reward Ratio: {custom_metrics.get('Risk-to-Reward Ratio', 'N/A'):.2f}")
                print(f"  • Execution Time: {results.get('execution_time', 'N/A'):.2f}s")
                
                print("\n🎯 API Integration Test: PASSED")
                return True
            else:
                print("❌ No results in response")
                return False
                
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ API call timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 Could not connect to API server")
        print("💡 Make sure the FastAPI server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False


def test_hybrid_validation_api():
    """Test the hybrid backtest validation API endpoint."""
    print("\n🔍 Testing Hybrid Backtest Validation API")
    print("="*50)
    
    # Test validation data
    validation_data = {
        "symbol": "NIFTY50",
        "timeframe": 30,
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "initial_capital": 30000.0,
        "margin": 0.1,
        "commission": 0.0
    }
    
    try:
        print("🔍 Sending validation request...")
        
        response = requests.post(
            f"{BASE_URL}/hybrid/validate",
            json=validation_data,
            timeout=30
        )
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Validation API call successful!")
            
            print(f"  • Is Valid: {result.get('is_valid', 'N/A')}")
            print(f"  • Issues: {len(result.get('issues', []))}")
            print(f"  • Recommendations: {len(result.get('recommendations', []))}")
            
            if result.get('issues'):
                print("  ⚠️  Issues found:")
                for issue in result['issues']:
                    print(f"    - {issue}")
            
            if result.get('recommendations'):
                print("  💡 Recommendations:")
                for rec in result['recommendations']:
                    print(f"    - {rec}")
            
            return True
        else:
            print(f"❌ Validation API call failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Validation API test failed: {e}")
        return False


def main():
    """Main test function."""
    print("🧪 Hybrid Backtesting API Integration Tests")
    print("="*60)
    
    # Test validation endpoint first
    validation_success = test_hybrid_validation_api()
    
    # Test main backtest endpoint
    backtest_success = test_hybrid_backtest_api()
    
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    print(f"Validation API: {'✅ PASSED' if validation_success else '❌ FAILED'}")
    print(f"Backtest API:   {'✅ PASSED' if backtest_success else '❌ FAILED'}")
    
    if validation_success and backtest_success:
        print("\n🎉 All API integration tests PASSED!")
        print("💡 The hybrid backtesting system is ready for UI integration")
        return 0
    else:
        print("\n❌ Some API tests FAILED")
        print("💡 Check the FastAPI server and database connectivity")
        return 1


if __name__ == "__main__":
    exit(main())
